
import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calculator, Package, User, FactoryIcon, Palette, Save, FileText, Layers } from "lucide-react";
import { formatLibyanDinar } from "@/utils/calculations";
import { useToast } from "@/hooks/use-toast";
import {
  getMaterials,
  getWorkers,
  getFactories,
  getDesigners,
  addProject,
  Material,
  Worker,
  Factory,
  Designer,
  Project
} from "@/utils/dataManager";
import { ProjectMaterialsSummary } from "@/types/materials";
import MaterialSelector from "./MaterialSelector";

interface CostCalculatorProps {
  onStatsUpdate?: () => void;
}

const CostCalculator = ({ onStatsUpdate }: CostCalculatorProps) => {
  const { toast } = useToast();
  const [customerName, setCustomerName] = useState("");
  const [phone, setPhone] = useState("");
  const [area, setArea] = useState(0);
  const [furnitureType, setFurnitureType] = useState("");
  const [materials, setMaterials] = useState<Material[]>([]);
  const [workers, setWorkers] = useState<Worker[]>([]);
  const [factories, setFactories] = useState<Factory[]>([]);
  const [designers, setDesigners] = useState<Designer[]>([]);
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null);
  const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);
  const [selectedFactory, setSelectedFactory] = useState<Factory | null>(null);
  const [selectedDesigner, setSelectedDesigner] = useState<Designer | null>(null);
  const [downPayment, setDownPayment] = useState("");
  const [notes, setNotes] = useState("");
  const [materialsSummary, setMaterialsSummary] = useState<ProjectMaterialsSummary | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [materialsData, workersData, factoriesData, designersData] = await Promise.all([
        getMaterials(),
        getWorkers(),
        getFactories(),
        getDesigners()
      ]);

      setMaterials(materialsData);
      setWorkers(workersData);
      setFactories(factoriesData);
      setDesigners(designersData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      // تعيين مصفوفات فارغة في حالة الخطأ
      setMaterials([]);
      setWorkers([]);
      setFactories([]);
      setDesigners([]);
    }
  };

  const calculateMaterialCost = () => {
    return selectedMaterial ? area * selectedMaterial.pricePerSqm : 0;
  };

  const calculateWorkerCost = () => {
    return selectedWorker ? area * selectedWorker.pricePerSqm : 0;
  };

  const calculateFactoryCost = () => {
    return selectedFactory ? area * selectedFactory.pricePerSqm : 0;
  };

  const calculateDesignerCost = () => {
    return selectedDesigner ? area * selectedDesigner.pricePerSqm : 0;
  };

  const calculateBreakdown = () => {
    return {
      materialCost: calculateMaterialCost(),
      workerCost: calculateWorkerCost(),
      factoryCost: calculateFactoryCost(),
      designerCost: calculateDesignerCost(),
    };
  };

  const calculateTotalCost = () => {
    const breakdown = calculateBreakdown();
    // إزالة تكلفة المادة الأساسية واستبدالها بالمواد التفصيلية
    const basicCost = breakdown.workerCost + breakdown.factoryCost + breakdown.designerCost;
    const detailedMaterialsCost = materialsSummary ? materialsSummary.totalSaleCost : 0;
    return basicCost + detailedMaterialsCost;
  };

  const calculateFirstPayment = () => {
    return calculateTotalCost() * 0.7; // 70%
  };

  const calculateFinalPayment = () => {
    return calculateTotalCost() * 0.3; // 30%
  };

  const handleMaterialsChange = (summary: ProjectMaterialsSummary) => {
    setMaterialsSummary(summary);
  };

  const handleSaveProject = async () => {
    if (!customerName || !selectedWorker || !selectedFactory || !selectedDesigner || area <= 0) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء جميع الحقول المطلوبة (اسم العميل، المساحة، العامل، المصنع، المصمم)",
        variant: "destructive",
      });
      return;
    }

    const breakdown = calculateBreakdown();
    const totalCost = calculateTotalCost();
    const paidAmount = parseFloat(downPayment) || 0;

    const newProject: Omit<Project, 'id'> = {
      customerName,
      customerPhone: phone || undefined,
      area,
      furnitureType,
      selectedMaterial: null, // لا نحتاج مادة أساسية بعد الآن
      selectedWorker,
      selectedFactory,
      selectedDesigner,
      totalCost,
      breakdown: {
        ...breakdown,
        materialCost: materialsSummary ? materialsSummary.totalSaleCost : 0 // استخدام تكلفة المواد التفصيلية
      },
      paidAmount,
      remainingAmount: totalCost - paidAmount,
      status: 'قيد التنفيذ',
      invoiceStatus: 'مبدئية',
      createdAt: new Date().toISOString(),
      notes: notes || undefined,
      materialsSummary // إضافة ملخص المواد التفصيلية
    };

    try {
      await addProject(newProject);

      if (onStatsUpdate) {
        onStatsUpdate();
      }

      // مسح النموذج
      setCustomerName("");
      setPhone("");
      setArea(0);
      setFurnitureType("");
      setSelectedWorker(null);
      setSelectedFactory(null);
      setSelectedDesigner(null);
      setDownPayment("");
      setNotes("");
      setMaterialsSummary(null);

      toast({
        title: "تم حفظ المشروع",
        description: "تم إنشاء المشروع والفاتورة المبدئية بنجاح",
      });
    } catch (error) {
      console.error('خطأ في حفظ المشروع:', error);
      toast({
        title: "خطأ في الحفظ",
        description: "حدث خطأ أثناء حفظ المشروع",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="grid gap-4">
      {/* معلومات العميل */}
      <Card className="shadow-md">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-md font-semibold flex items-center gap-2">
            <User className="h-4 w-4" />
            معلومات العميل
          </CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">اسم العميل</Label>
              <Input
                id="name"
                placeholder="أدخل اسم العميل"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">رقم الهاتف</Label>
              <Input
                id="phone"
                placeholder="أدخل رقم الهاتف"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* تبويبات المشروع */}
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">المعلومات الأساسية</TabsTrigger>
          <TabsTrigger value="materials">اختيار المواد</TabsTrigger>
          <TabsTrigger value="summary">ملخص التكلفة</TabsTrigger>
        </TabsList>

        {/* تبويبة المعلومات الأساسية */}
        <TabsContent value="basic" className="space-y-4">
          {/* تفاصيل المشروع */}
          <Card className="shadow-md">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-md font-semibold flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            تفاصيل المشروع
          </CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="area">المساحة (م²)</Label>
              <Input
                id="area"
                type="number"
                placeholder="أدخل مساحة المشروع"
                value={area}
                onChange={(e) => setArea(parseFloat(e.target.value))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="furnitureType">نوع الأثاث</Label>
              <Input
                id="furnitureType"
                placeholder="أدخل نوع الأثاث"
                value={furnitureType}
                onChange={(e) => setFurnitureType(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="worker">العامل</Label>
            <Select onValueChange={(value) => setSelectedWorker(workers.find(w => w.id === value) || null)}>
              <SelectTrigger>
                <SelectValue placeholder="اختر العامل" />
              </SelectTrigger>
              <SelectContent>
                {workers.map((worker) => (
                  <SelectItem key={worker.id} value={worker.id}>
                    {worker.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="factory">المصنع</Label>
              <Select onValueChange={(value) => setSelectedFactory(factories.find(f => f.id === value) || null)}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر المصنع" />
                </SelectTrigger>
                <SelectContent>
                  {factories.map((factory) => (
                    <SelectItem key={factory.id} value={factory.id}>
                      {factory.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="designer">المصمم</Label>
              <Select onValueChange={(value) => setSelectedDesigner(designers.find(d => d.id === value) || null)}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر المصمم" />
                </SelectTrigger>
                <SelectContent>
                  {designers.map((designer) => (
                    <SelectItem key={designer.id} value={designer.id}>
                      {designer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
        </TabsContent>

        {/* تبويبة اختيار المواد */}
        <TabsContent value="materials" className="space-y-4">
          <MaterialSelector onMaterialsChange={handleMaterialsChange} />
        </TabsContent>

        {/* تبويبة ملخص التكلفة */}
        <TabsContent value="summary" className="space-y-4">
          {/* تفاصيل التكلفة */}
      <Card className="shadow-md">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-md font-semibold flex items-center gap-2">
            <Palette className="h-4 w-4" />
            تفاصيل التكلفة
          </CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4">
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <Label>تكلفة العامل</Label>
              <Input
                value={formatLibyanDinar(calculateWorkerCost())}
                readOnly
              />
            </div>
            <div>
              <Label>تكلفة المصنع</Label>
              <Input
                value={formatLibyanDinar(calculateFactoryCost())}
                readOnly
              />
            </div>
            <div>
              <Label>تكلفة المصمم</Label>
              <Input
                value={formatLibyanDinar(calculateDesignerCost())}
                readOnly
              />
            </div>
          </div>

          {/* تكلفة المواد التفصيلية */}
          {materialsSummary && (
            <div className="grid md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg">
              <div>
                <Label>تكلفة المواد التفصيلية</Label>
                <Input
                  value={formatLibyanDinar(materialsSummary.totalSaleCost)}
                  readOnly
                  className="bg-blue-100"
                />
              </div>
              <div>
                <Label>مكسب المواد</Label>
                <Input
                  value={formatLibyanDinar(materialsSummary.totalProfit)}
                  readOnly
                  className="bg-green-100"
                />
              </div>
            </div>
          )}

          <div>
            <Label>التكلفة الإجمالية</Label>
            <Input
              className="font-bold text-xl"
              value={formatLibyanDinar(calculateTotalCost())}
              readOnly
            />
          </div>

          {/* تقسيم الدفع */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>الدفعة الأولى (70%)</Label>
              <Input
                className="text-blue-600 font-semibold"
                value={formatLibyanDinar(calculateFirstPayment())}
                readOnly
              />
            </div>
            <div className="space-y-2">
              <Label>الدفعة النهائية (30%)</Label>
              <Input
                className="text-orange-600 font-semibold"
                value={formatLibyanDinar(calculateFinalPayment())}
                readOnly
              />
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="downPayment">الدفعة المقدمة (د.ل)</Label>
              <Input
                id="downPayment"
                type="number"
                placeholder="أدخل الدفعة المقدمة"
                value={downPayment}
                onChange={(e) => setDownPayment(e.target.value)}
              />
            </div>
            <div>
              <Label>المبلغ المتبقي</Label>
              <Input
                className="text-green-600 font-semibold"
                value={formatLibyanDinar(calculateTotalCost() - (parseFloat(downPayment) || 0))}
                readOnly
              />
            </div>
          </div>
        </CardContent>
      </Card>

          {/* ملاحظات */}
          <Card className="shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-md font-semibold flex items-center gap-2">
                <FileText className="h-4 w-4" />
                ملاحظات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="أدخل ملاحظات إضافية حول المشروع"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </CardContent>
          </Card>

          {/* حفظ المشروع */}
          <Button onClick={handleSaveProject} className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2 justify-center">
            <Save className="h-4 w-4" />
            حفظ المشروع (إنشاء فاتورة مبدئية)
          </Button>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CostCalculator;
