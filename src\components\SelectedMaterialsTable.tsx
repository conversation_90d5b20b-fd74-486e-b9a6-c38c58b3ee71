import React from 'react';
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Package,
  ShoppingCart,
  TrendingUp,
  AlertCircle,
  Printer
} from 'lucide-react';
import { ProjectMaterialsSummary } from '@/types/materials';
import { formatLibyanDinar } from '@/utils/calculations';

interface SelectedMaterialsTableProps {
  materialsSummary: ProjectMaterialsSummary | null;
}

const SelectedMaterialsTable: React.FC<SelectedMaterialsTableProps> = ({
  materialsSummary
}) => {

  const handlePrint = () => {
    const printContent = document.getElementById('materials-table-print');
    if (printContent) {
      const originalContent = document.body.innerHTML;
      const printWindow = window.open('', '_blank');

      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>قائمة المواد المختارة</title>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  direction: rtl;
                  margin: 20px;
                  color: #333;
                }
                .header {
                  text-align: center;
                  margin-bottom: 30px;
                  border-bottom: 2px solid #333;
                  padding-bottom: 10px;
                }
                .header h1 {
                  margin: 0;
                  color: #2563eb;
                }
                .header p {
                  margin: 5px 0;
                  color: #666;
                }
                table {
                  width: 100%;
                  border-collapse: collapse;
                  margin-top: 20px;
                }
                th, td {
                  border: 1px solid #ddd;
                  padding: 12px;
                  text-align: right;
                }
                th {
                  background-color: #f8f9fa;
                  font-weight: bold;
                  color: #2563eb;
                }
                tr:nth-child(even) {
                  background-color: #f9f9f9;
                }
                .material-code {
                  font-family: monospace;
                  font-weight: bold;
                  color: #1f2937;
                }
                .source-badge {
                  padding: 4px 8px;
                  border-radius: 4px;
                  font-size: 12px;
                  font-weight: bold;
                }
                .source-stock {
                  background-color: #dcfce7;
                  color: #166534;
                  border: 1px solid #bbf7d0;
                }
                .source-external {
                  background-color: #fed7aa;
                  color: #9a3412;
                  border: 1px solid #fdba74;
                }
                .footer {
                  margin-top: 30px;
                  text-align: center;
                  font-size: 12px;
                  color: #666;
                  border-top: 1px solid #ddd;
                  padding-top: 10px;
                }
                @media print {
                  body { margin: 0; }
                  .no-print { display: none; }
                }
              </style>
            </head>
            <body>
              <div class="header">
                <h1>قائمة المواد المختارة للمشروع</h1>
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                <p>عدد المواد: ${materialsSummary?.materials.length || 0} مادة</p>
              </div>
              ${printContent.innerHTML}
              <div class="footer">
                <p>نظام إدارة مصنع الأثاث - تم إنشاؤه تلقائياً</p>
              </div>
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
      }
    }
  };
  if (!materialsSummary || materialsSummary.materials.length === 0) {
    return (
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            المواد المختارة للمشروع
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">لم يتم اختيار أي مواد بعد</p>
            <p className="text-sm">انتقل إلى تبويبة "اختيار المواد" لإضافة المواد المطلوبة للمشروع</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getCategoryColor = (materialCode: string) => {
    // تحديد لون حسب نوع المادة
    if (materialCode.startsWith('H-')) return '#10B981'; // إكسسوارات - أخضر
    if (materialCode.includes('MDF') || materialCode.includes('AGT')) return '#3B82F6'; // خامات أساسية - أزرق
    if (materialCode.includes('SEALER') || materialCode.includes('DUCO')) return '#F59E0B'; // مواد طلاء - برتقالي
    return '#6B7280'; // افتراضي - رمادي
  };

  const getSourceBadge = (source: 'external' | 'stock') => {
    if (source === 'stock') {
      return (
        <span className="source-badge source-stock">
          من المخزن
        </span>
      );
    } else {
      return (
        <span className="source-badge source-external">
          خارجي
        </span>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* أنماط CSS للطباعة */}
      <style jsx>{`
        .source-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: bold;
          display: inline-block;
        }
        .source-stock {
          background-color: #dcfce7;
          color: #166534;
          border: 1px solid #bbf7d0;
        }
        .source-external {
          background-color: #fed7aa;
          color: #9a3412;
          border: 1px solid #fdba74;
        }
        .material-code {
          font-family: monospace;
          font-weight: bold;
          color: #1f2937;
        }
      `}</style>
      {/* ملخص سريع */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Package className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm text-blue-600 font-medium">عدد المواد</p>
                <p className="text-xl font-bold text-blue-800">
                  {materialsSummary.materials.length} مادة
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Printer className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm text-green-600 font-medium">طباعة القائمة</p>
                <Button
                  onClick={handlePrint}
                  className="mt-2 bg-green-600 hover:bg-green-700 text-white"
                  size="sm"
                >
                  <Printer className="h-4 w-4 mr-2" />
                  طباعة
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* جدول المواد التفصيلي */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            قائمة المواد المختارة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto" id="materials-table-print">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">رقم المادة</TableHead>
                  <TableHead className="text-right">اسم المادة</TableHead>
                  <TableHead className="text-right">الكمية المطلوبة</TableHead>
                  <TableHead className="text-right">المصدر</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {materialsSummary.materials.map((material, index) => (
                  <TableRow key={`${material.materialId}-${index}`} className="hover:bg-gray-50">
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: getCategoryColor(material.materialCode) }}
                        />
                        <span className="material-code font-mono font-semibold">
                          {material.materialCode}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="font-medium text-gray-900">
                        {material.materialName}
                      </div>
                    </TableCell>

                    <TableCell>
                      <span className="font-semibold text-blue-600 text-lg">
                        {material.requiredQuantity}
                      </span>
                    </TableCell>

                    <TableCell>
                      {getSourceBadge(material.source)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SelectedMaterialsTable;
